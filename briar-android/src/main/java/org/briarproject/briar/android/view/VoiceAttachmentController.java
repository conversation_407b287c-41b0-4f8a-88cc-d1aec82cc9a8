package org.briarproject.briar.android.view;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Parcel;
import android.os.Parcelable;
import android.widget.Toast;

import org.briarproject.briar.R;
import org.briarproject.briar.android.attachment.AttachmentManager;
import org.briarproject.briar.android.voice.VoiceMessageRecorder;
import org.briarproject.briar.android.view.ImagePreview.ImagePreviewListener;
import org.briarproject.nullsafety.NotNullByDefault;

import java.io.File;
import java.io.IOException;
import java.util.logging.Logger;

import androidx.annotation.Nullable;
import androidx.annotation.UiThread;
import androidx.core.content.ContextCompat;
import androidx.customview.view.AbsSavedState;

import static android.view.View.VISIBLE;
import static android.widget.Toast.LENGTH_LONG;
import static androidx.customview.view.AbsSavedState.EMPTY_STATE;
import static java.util.logging.Level.WARNING;
import static org.briarproject.briar.android.view.TextSendController.SendState.SENT;

/**
 * 语音附件控制器
 * 
 * 扩展TextAttachmentController以支持语音消息录制和发送。
 */
@UiThread
@NotNullByDefault
public class VoiceAttachmentController extends TextAttachmentController 
		implements VoiceMessageRecorder.VoiceRecorderListener {

	private static final Logger LOG = Logger.getLogger(VoiceAttachmentController.class.getName());
	
	private final VoiceAttachmentListener voiceListener;
	private final CompositeSendButton sendButton;
	
	@Nullable
	private VoiceMessageRecorder voiceRecorder;
	@Nullable
	private File currentVoiceFile;
	private boolean isRecordingVoice = false;
	private boolean hasVoiceMessage = false;
	
	public VoiceAttachmentController(TextInputView v, ImagePreview imagePreview,
			VoiceAttachmentListener listener, AttachmentManager attachmentManager) {
		super(v, imagePreview, listener, attachmentManager);
		this.voiceListener = listener;
		this.sendButton = (CompositeSendButton) compositeSendButton;
		
		// 设置语音按钮点击事件
		sendButton.setOnVoiceClickListener(view -> onVoiceButtonClicked());
	}
	
	@Override
	protected void updateViewState() {
		super.updateViewState();
		
		if (isRecordingVoice) {
			// 录制状态：显示录制进度
			sendButton.showProgress(true);
		} else if (hasVoiceMessage) {
			// 有语音消息：显示发送按钮
			sendButton.showAttachmentButtons(false, isSendButtonEnabled(), false);
		} else if (textIsEmpty && !hasImages()) {
			// 空文本且无图片：显示语音和图片按钮
			boolean hasVoiceSupport = sendButton.hasVoiceSupport();
			boolean hasImageSupport = sendButton.hasImageSupport();
			sendButton.showAttachmentButtons(hasImageSupport, false, hasVoiceSupport);
		}
	}
	
	@Override
	protected boolean canSendEmptyText() {
		return super.canSendEmptyText() || hasVoiceMessage;
	}
	
	@Override
	protected CharSequence getCurrentTextHint() {
		if (hasVoiceMessage) {
			Context ctx = textInput.getContext();
			return ctx.getString(R.string.voice_message_caption_hint);
		}
		return super.getCurrentTextHint();
	}
	
	@Override
	public void onSendEvent() {
		if (canSend()) {
			if (isRecordingVoice) {
				// 如果正在录制，先停止录制
				stopVoiceRecording();
				return;
			}
			
			// 发送消息（包括语音消息）
			super.onSendEvent();
		}
	}
	
	@Override
	protected void onSendStateChanged(SendState sendState) {
		super.onSendStateChanged(sendState);
		if (sendState == SENT) {
			resetVoiceMessage();
		}
	}
	
	/**
	 * 设置语音支持
	 */
	public void setVoiceSupported() {
		sendButton.setVoiceSupported();
		updateViewState();
	}
	
	/**
	 * 检查是否有图片附件
	 */
	private boolean hasImages() {
		// 通过检查是否可以发送空文本来判断是否有图片附件
		// 如果有图片附件，canSendEmptyText()会返回true
		return canSendEmptyText();
	}
	
	/**
	 * 语音按钮点击处理
	 */
	private void onVoiceButtonClicked() {
		if (!sendButton.hasVoiceSupport()) {
			showVoiceNotSupportedDialog();
			return;
		}
		
		if (!hasRecordPermission()) {
			voiceListener.onRequestRecordPermission();
			return;
		}
		
		if (isRecordingVoice) {
			stopVoiceRecording();
		} else {
			startVoiceRecording();
		}
	}
	
	/**
	 * 检查录音权限
	 */
	private boolean hasRecordPermission() {
		Context context = textInput.getContext();
		return ContextCompat.checkSelfPermission(context, 
			Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED;
	}
	
	/**
	 * 显示不支持语音的对话框
	 */
	private void showVoiceNotSupportedDialog() {
		Context ctx = textInput.getContext();
		Toast.makeText(ctx, ctx.getString(R.string.voice_message_not_supported), 
			LENGTH_LONG).show();
	}
	
	/**
	 * 开始语音录制
	 */
	private void startVoiceRecording() {
		if (voiceRecorder == null) {
			// 如果录音器被释放了，就重新创建一个
			voiceRecorder = new VoiceMessageRecorder(textInput.getContext(), this);
		}
		
		try {
			// 创建临时文件
			Context context = textInput.getContext();
			File tempDir = new File(context.getCacheDir(), "voice_messages");
			if (!tempDir.exists() && !tempDir.mkdirs()) {
				onRecordingError("无法创建录音目录");
				return;
			}
			
			currentVoiceFile = File.createTempFile("voice_", ".aac", tempDir);
			
			if (voiceRecorder.startRecording(currentVoiceFile)) {
				isRecordingVoice = true;
				updateViewState();
				voiceListener.onVoiceRecordingStarted();
			}
			
		} catch (IOException e) {
			LOG.log(WARNING, "Failed to create voice file", e);
			onRecordingError("创建录音文件失败");
		}
	}
	
	/**
	 * 停止语音录制
	 */
	private void stopVoiceRecording() {
		if (voiceRecorder != null && isRecordingVoice) {
			File recordedFile = voiceRecorder.stopRecording();
			if (recordedFile != null && recordedFile.exists()) {
				// 录制成功，保存语音消息
				currentVoiceFile = recordedFile;
				hasVoiceMessage = true;
			}
		}
		
		isRecordingVoice = false;
		updateViewState();
	}
	
	/**
	 * 取消语音录制
	 */
	public void cancelVoiceRecording() {
		if (voiceRecorder != null && isRecordingVoice) {
			voiceRecorder.cancelRecording();
		}
		resetVoiceMessage();
	}
	
	/**
	 * 重置语音消息状态
	 */
	private void resetVoiceMessage() {
		isRecordingVoice = false;
		hasVoiceMessage = false;
		
		if (currentVoiceFile != null && currentVoiceFile.exists()) {
			if (!currentVoiceFile.delete()) {
				LOG.warning("Failed to delete voice file");
			}
		}
		currentVoiceFile = null;
		
		updateViewState();
	}
	
	@Override
	public void onCancel() {
		super.onCancel();
		cancelVoiceRecording();
	}
	
	// VoiceRecorderListener 实现
	
	@Override
	public void onRecordingStarted() {
		LOG.info("Voice recording started");
	}
	
	@Override
	public void onRecordingProgress(long currentDuration, long remainingTime) {
		voiceListener.onVoiceRecordingProgress(currentDuration, remainingTime);
	}
	
	@Override
	public void onRecordingCompleted(File audioFile, long duration) {
		LOG.info("Voice recording completed: " + duration + "ms");
		voiceListener.onVoiceRecordingCompleted(audioFile, duration);
	}
	
	@Override
	public void onRecordingCancelled() {
		LOG.info("Voice recording cancelled");
		resetVoiceMessage();
		voiceListener.onVoiceRecordingCancelled();
	}
	
	@Override
	public void onRecordingError(String error) {
		LOG.warning("Voice recording error: " + error);
		resetVoiceMessage();
		Toast.makeText(textInput.getContext(), error, LENGTH_LONG).show();
		voiceListener.onVoiceRecordingError(error);
	}
	
	@Override
	public void onMaxDurationReached() {
		LOG.info("Voice recording max duration reached");
		stopVoiceRecording();
		voiceListener.onVoiceRecordingMaxDurationReached();
	}
	
	/**
	 * 释放资源
	 */
	public void release() {
		if (voiceRecorder != null) {
			voiceRecorder.release();
			voiceRecorder = null;
		}
		resetVoiceMessage();
	}
	
	/**
	 * 语音附件监听器接口
	 */
	@UiThread
	public interface VoiceAttachmentListener extends AttachmentListener {
		
		/**
		 * 请求录音权限
		 */
		void onRequestRecordPermission();
		
		/**
		 * 语音录制开始
		 */
		void onVoiceRecordingStarted();
		
		/**
		 * 语音录制进度
		 */
		void onVoiceRecordingProgress(long currentDuration, long remainingTime);
		
		/**
		 * 语音录制完成
		 */
		void onVoiceRecordingCompleted(File audioFile, long duration);
		
		/**
		 * 语音录制取消
		 */
		void onVoiceRecordingCancelled();
		
		/**
		 * 语音录制错误
		 */
		void onVoiceRecordingError(String error);
		
		/**
		 * 达到最大录制时长
		 */
		void onVoiceRecordingMaxDurationReached();
	}
}
