<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:tools="http://schemas.android.com/tools"
	package="org.briarproject.briar">

	<uses-feature
		android:name="android.hardware.bluetooth"
		android:required="false" />
	<uses-feature
		android:name="android.hardware.camera"
		android:required="false" />
	<uses-feature
		android:name="android.hardware.microphone"
		android:required="false" />
	<uses-feature
		android:name="android.hardware.touchscreen"
		android:required="false" />

	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
	<uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
	<uses-permission
		android:name="android.permission.NEARBY_WIFI_DEVICES"
		android:usesPermissionFlags="neverForLocation"
		tools:targetApi="31" />
	<uses-permission android:name="android.permission.CAMERA" />
	<uses-permission android:name="android.permission.RECORD_AUDIO" />
	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
	<!--suppress DeprecatedClassUsageInspection -->
	<uses-permission android:name="android.permission.USE_FINGERPRINT" />
	<uses-permission android:name="android.permission.VIBRATE" />
	<uses-permission android:name="android.permission.WAKE_LOCK" />
	<uses-permission
		android:name="android.permission.WRITE_EXTERNAL_STORAGE"
		android:maxSdkVersion="18"
		tools:ignore="ScopedStorage" />
	<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

	<uses-permission-sdk-23
		android:name="android.permission.ACCESS_FINE_LOCATION"
		android:maxSdkVersion="32" />
	<uses-permission-sdk-23 android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
	<uses-permission-sdk-23 android:name="android.permission.USE_BIOMETRIC" />
	<uses-permission-sdk-23 android:name="android.permission.FOREGROUND_SERVICE" />
	<uses-permission-sdk-23 android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />
	<uses-permission-sdk-23 android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />

	<uses-permission
		android:name="android.permission.HIDE_OVERLAY_WINDOWS"
		tools:targetApi="31" />

	<application
		android:name="org.briarproject.briar.android.BriarApplicationImpl"
		android:allowBackup="false"
		android:dataExtractionRules="@xml/backup_extraction_rules"
		android:fullBackupContent="@xml/backup_rules"
		android:icon="@mipmap/ic_launcher_round"
		android:label="@string/app_name"
		android:logo="@mipmap/ic_launcher_round"
		android:networkSecurityConfig="@xml/network_security_config"
		android:supportsRtl="true"
		android:theme="@style/BriarTheme"
		tools:ignore="GoogleAppIndexingWarning,UnusedAttribute">

		<receiver
			android:name="org.briarproject.briar.android.login.SignInReminderReceiver"
			android:exported="false">
			<intent-filter>
				<action android:name="android.intent.action.BOOT_COMPLETED" />
				<action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
			</intent-filter>
		</receiver>

		<service
			android:name="org.briarproject.briar.android.BriarService"
			android:exported="false"
			android:foregroundServiceType="connectedDevice|dataSync">
			<intent-filter>
				<action android:name="org.briarproject.briar.android.BriarService" />
			</intent-filter>
		</service>

		<service
			android:name="org.briarproject.briar.android.NotificationCleanupService"
			android:exported="false" />

		<activity
			android:name="org.briarproject.briar.android.reporting.CrashReportActivity"
			android:excludeFromRecents="true"
			android:exported="false"
			android:finishOnTaskLaunch="true"
			android:label="@string/crash_report_title"
			android:launchMode="singleInstance"
			android:process=":briar_error_handler"
			android:theme="@style/BriarTheme.NoActionBar"
			android:windowSoftInputMode="adjustResize|stateHidden" />

		<activity
			android:name="org.briarproject.briar.android.reporting.FeedbackActivity"
			android:exported="false"
			android:label="@string/feedback_title"
			android:parentActivityName="org.briarproject.briar.android.settings.SettingsActivity"
			android:theme="@style/BriarTheme.NoActionBar"
			android:windowSoftInputMode="adjustResize|stateHidden">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.settings.SettingsActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.splash.ExpiredActivity"
			android:exported="false"
			android:label="@string/app_name" />

		<activity
			android:name="org.briarproject.briar.android.login.StartupActivity"
			android:exported="false"
			android:theme="@style/BriarTheme.NoActionBar" />

		<activity
			android:name="org.briarproject.briar.android.account.SetupActivity"
			android:exported="false"
			android:theme="@style/BriarTheme.NoActionBar" />

		<activity
			android:name="org.briarproject.briar.android.splash.SplashScreenActivity"
			android:exported="true"
			android:label="@string/app_name"
			android:theme="@style/BriarTheme.NoActionBar">
			<intent-filter>
				<action android:name="android.intent.action.MAIN" />

				<category android:name="android.intent.category.LAUNCHER" />
			</intent-filter>
		</activity>

		<activity
			android:name="org.briarproject.briar.android.navdrawer.NavDrawerActivity"
			android:exported="true"
			android:launchMode="singleTask"
			android:theme="@style/BriarTheme.NoActionBar">
			<intent-filter android:label="@string/add_contact_remotely_title_case">
				<action android:name="android.intent.action.VIEW" />

				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />

				<data android:scheme="briar" />
			</intent-filter>
			<intent-filter android:label="@string/add_contact_remotely_title_case">
				<action android:name="android.intent.action.SEND" />
				<category android:name="android.intent.category.DEFAULT" />
				<data android:mimeType="text/plain" />
			</intent-filter>
		</activity>

		<activity
			android:name="org.briarproject.briar.android.conversation.ConversationActivity"
			android:exported="false"
			android:label="@string/app_name"
			android:parentActivityName="org.briarproject.briar.android.navdrawer.NavDrawerActivity"
			android:theme="@style/BriarTheme.NoActionBar"
			android:windowSoftInputMode="adjustResize|stateUnchanged">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.navdrawer.NavDrawerActivity" />
		</activity>

		<activity
			android:name=".android.conversation.ImageActivity"
			android:exported="false"
			android:parentActivityName="org.briarproject.briar.android.conversation.ConversationActivity"
			android:theme="@style/BriarTheme.ActionBarOverlay">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.conversation.ConversationActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.privategroup.creation.CreateGroupActivity"
			android:exported="false"
			android:label="@string/groups_create_group_title"
			android:parentActivityName="org.briarproject.briar.android.navdrawer.NavDrawerActivity"
			android:windowSoftInputMode="adjustResize|stateAlwaysVisible">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.navdrawer.NavDrawerActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.privategroup.conversation.GroupActivity"
			android:exported="false"
			android:label="@string/app_name"
			android:parentActivityName="org.briarproject.briar.android.navdrawer.NavDrawerActivity"
			android:theme="@style/BriarTheme.NoActionBar"
			android:windowSoftInputMode="adjustResize|stateHidden">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.navdrawer.NavDrawerActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.privategroup.invitation.GroupInvitationActivity"
			android:exported="false"
			android:label="@string/groups_invitations_title"
			android:parentActivityName="org.briarproject.briar.android.navdrawer.NavDrawerActivity">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.navdrawer.NavDrawerActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.privategroup.memberlist.GroupMemberListActivity"
			android:exported="false"
			android:label="@string/groups_member_list"
			android:parentActivityName="org.briarproject.briar.android.privategroup.conversation.GroupActivity">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.privategroup.conversation.GroupActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.privategroup.reveal.RevealContactsActivity"
			android:exported="false"
			android:label="@string/groups_reveal_contacts"
			android:parentActivityName="org.briarproject.briar.android.privategroup.conversation.GroupActivity">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.privategroup.conversation.GroupActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.privategroup.creation.GroupInviteActivity"
			android:exported="false"
			android:label="@string/groups_invite_members"
			android:parentActivityName="org.briarproject.briar.android.privategroup.conversation.GroupActivity"
			android:windowSoftInputMode="adjustResize|stateHidden">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.privategroup.conversation.GroupActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.sharing.ForumInvitationActivity"
			android:exported="false"
			android:label="@string/forum_invitations_title"
			android:parentActivityName="org.briarproject.briar.android.navdrawer.NavDrawerActivity">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.navdrawer.NavDrawerActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.sharing.BlogInvitationActivity"
			android:exported="false"
			android:label="@string/blogs_sharing_invitations_title"
			android:parentActivityName="org.briarproject.briar.android.conversation.ConversationActivity">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.conversation.ConversationActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.forum.CreateForumActivity"
			android:exported="false"
			android:label="@string/create_forum_title"
			android:parentActivityName="org.briarproject.briar.android.navdrawer.NavDrawerActivity"
			android:windowSoftInputMode="adjustResize|stateAlwaysVisible">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.navdrawer.NavDrawerActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.forum.ForumActivity"
			android:exported="false"
			android:label="@string/app_name"
			android:parentActivityName="org.briarproject.briar.android.navdrawer.NavDrawerActivity"
			android:theme="@style/BriarTheme.NoActionBar"
			android:windowSoftInputMode="adjustResize|stateHidden">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.navdrawer.NavDrawerActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.sharing.ShareForumActivity"
			android:exported="false"
			android:label="@string/activity_share_toolbar_header"
			android:parentActivityName="org.briarproject.briar.android.forum.ForumActivity"
			android:windowSoftInputMode="adjustResize|stateHidden">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.forum.ForumActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.sharing.ShareBlogActivity"
			android:exported="false"
			android:label="@string/activity_share_toolbar_header"
			android:parentActivityName="org.briarproject.briar.android.blog.BlogActivity"
			android:windowSoftInputMode="adjustResize|stateHidden">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.blog.BlogActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.sharing.ForumSharingStatusActivity"
			android:exported="false"
			android:label="@string/sharing_status"
			android:parentActivityName="org.briarproject.briar.android.forum.ForumActivity">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.forum.ForumActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.sharing.BlogSharingStatusActivity"
			android:exported="false"
			android:label="@string/sharing_status"
			android:parentActivityName="org.briarproject.briar.android.blog.BlogActivity">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.blog.BlogActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.blog.BlogActivity"
			android:exported="false"
			android:parentActivityName="org.briarproject.briar.android.navdrawer.NavDrawerActivity"
			android:theme="@style/BriarTheme.NoActionBar">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.navdrawer.NavDrawerActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.blog.WriteBlogPostActivity"
			android:exported="false"
			android:label="@string/blogs_write_blog_post"
			android:parentActivityName="org.briarproject.briar.android.blog.BlogActivity"
			android:windowSoftInputMode="adjustResize|stateAlwaysVisible">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.blog.BlogActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.blog.ReblogActivity"
			android:exported="false"
			android:label="@string/blogs_reblog_button"
			android:parentActivityName="org.briarproject.briar.android.blog.BlogActivity"
			android:windowSoftInputMode="adjustResize|stateHidden">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.blog.BlogActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.contact.add.nearby.AddNearbyContactActivity"
			android:exported="false"
			android:label="@string/add_contact_title"
			android:parentActivityName="org.briarproject.briar.android.navdrawer.NavDrawerActivity"
			android:theme="@style/BriarTheme.NoActionBar">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.navdrawer.NavDrawerActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.introduction.IntroductionActivity"
			android:exported="false"
			android:label="@string/introduction_activity_title"
			android:parentActivityName="org.briarproject.briar.android.conversation.ConversationActivity"
			android:windowSoftInputMode="adjustResize|stateHidden">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.conversation.ConversationActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.StartupFailureActivity"
			android:excludeFromRecents="true"
			android:exported="false"
			android:finishOnTaskLaunch="true"
			android:label="@string/startup_failed_activity_title"
			android:launchMode="singleInstance"
			android:process=":briar_startup_failure"
			android:windowSoftInputMode="adjustResize|stateHidden" />

		<activity
			android:name="org.briarproject.briar.android.settings.SettingsActivity"
			android:exported="false"
			android:label="@string/settings_button"
			android:parentActivityName="org.briarproject.briar.android.navdrawer.NavDrawerActivity"
			android:permission="android.permission.READ_NETWORK_USAGE_HISTORY">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.navdrawer.NavDrawerActivity" />
			<intent-filter>
				<action android:name="android.intent.action.MANAGE_NETWORK_USAGE" />
				<category android:name="android.intent.category.DEFAULT" />
			</intent-filter>
		</activity>

		<activity
			android:name="org.briarproject.briar.android.navdrawer.TransportsActivity"
			android:exported="false"
			android:label="@string/network_settings_title"
			android:parentActivityName="org.briarproject.briar.android.navdrawer.NavDrawerActivity">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.navdrawer.NavDrawerActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.login.ChangePasswordActivity"
			android:exported="false"
			android:label="@string/change_password"
			android:parentActivityName="org.briarproject.briar.android.settings.SettingsActivity"
			android:windowSoftInputMode="adjustResize|stateAlwaysVisible">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.settings.SettingsActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.test.TestDataActivity"
			android:exported="false"
			android:label="Create test data"
			android:parentActivityName="org.briarproject.briar.android.settings.SettingsActivity">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.settings.SettingsActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.panic.PanicPreferencesActivity"
			android:exported="false"
			android:label="@string/panic_setting"
			android:parentActivityName="org.briarproject.briar.android.settings.SettingsActivity">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.settings.SettingsActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.panic.PanicResponderActivity"
			android:exported="true"
			android:noHistory="true"
			android:theme="@style/TranslucentTheme">
			<!-- this can never have launchMode singleTask or singleInstance! -->
			<intent-filter>
				<action android:name="info.guardianproject.panic.action.TRIGGER" />
				<category android:name="android.intent.category.DEFAULT" />
			</intent-filter>
		</activity>

		<activity
			android:name="org.briarproject.briar.android.logout.ExitActivity"
			android:exported="false"
			android:theme="@android:style/Theme.NoDisplay" />

		<activity
			android:name=".android.logout.HideUiActivity"
			android:exported="false"
			android:theme="@android:style/Theme.NoDisplay" />

		<activity
			android:name=".android.account.UnlockActivity"
			android:exported="false"
			android:label="@string/lock_unlock"
			android:launchMode="singleTask"
			android:theme="@style/BriarTheme.NoActionBar" />

		<activity
			android:name=".android.contact.add.remote.AddContactActivity"
			android:exported="false"
			android:label="@string/add_contact_remotely_title_case"
			android:theme="@style/BriarTheme"
			android:windowSoftInputMode="adjustResize|stateHidden" />

		<activity
			android:name="org.briarproject.briar.android.blog.RssFeedActivity"
			android:exported="false"
			android:label="@string/blogs_rss_feeds"
			android:parentActivityName="org.briarproject.briar.android.navdrawer.NavDrawerActivity">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.navdrawer.NavDrawerActivity" />
		</activity>

		<activity
			android:name="org.briarproject.briar.android.removabledrive.RemovableDriveActivity"
			android:exported="false"
			android:label="@string/removable_drive_menu_title"
			android:parentActivityName="org.briarproject.briar.android.conversation.ConversationActivity">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.conversation.ConversationActivity" />
		</activity>

		<activity
			android:name=".android.contact.add.remote.PendingContactListActivity"
			android:exported="false"
			android:label="@string/pending_contact_requests"
			android:theme="@style/BriarTheme" />

		<activity
			android:name=".android.hotspot.HotspotActivity"
			android:exported="false"
			android:label="@string/hotspot_title"
			android:theme="@style/BriarTheme" />

		<activity
			android:name=".android.contact.connect.ConnectViaBluetoothActivity"
			android:exported="false"
			android:label="@string/connect_via_bluetooth_title"
			android:parentActivityName="org.briarproject.briar.android.conversation.ConversationActivity"
			android:theme="@style/BriarTheme">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.conversation.ConversationActivity" />
		</activity>

		<activity
			android:name=".android.mailbox.MailboxActivity"
			android:exported="false"
			android:label="@string/mailbox_settings_title"
			android:parentActivityName="org.briarproject.briar.android.settings.SettingsActivity"
			android:theme="@style/BriarTheme">
			<meta-data
				android:name="android.support.PARENT_ACTIVITY"
				android:value="org.briarproject.briar.android.settings.SettingsActivity" />
		</activity>

	</application>

	<queries>
		<package android:name="info.guardianproject.ripple" />

		<intent>
			<action android:name="android.intent.action.VIEW" />
			<data android:scheme="https" />
		</intent>
		<intent>
			<action android:name="android.intent.action.VIEW" />
			<data android:scheme="http" />
		</intent>
		<!-- white-listing the intents below does not seem necessary,
		but they are still included in case modified Android versions require it -->
		<intent>
			<action android:name="android.bluetooth.adapter.action.REQUEST_DISCOVERABLE" />
		</intent>
		<intent>
			<action android:name="android.settings.CHANNEL_NOTIFICATION_SETTINGS" />
		</intent>
	</queries>

</manifest>
