# Briar语音消息功能虚拟机调试指南

## 概述

本指南将帮助您在Android虚拟机上调试Briar项目的语音消息功能。我们已经修复了AndroidManifest.xml中缺少的RECORD_AUDIO权限问题。

## 前置条件

### 1. 开发环境
- Android Studio 已安装
- Android SDK 已配置
- Java 8 或更高版本
- Git 已安装

### 2. 虚拟机要求
- Android API 21 或更高版本
- 至少 2GB RAM
- 启用音频支持
- 启用麦克风支持

## 步骤1: 环境准备

### 1.1 检查项目状态
```bash
# 确认在项目根目录
cd /Volumes/ExtendData/Code/github/briar

# 检查Git状态
git status

# 确认权限修复
grep -n "RECORD_AUDIO" briar-android/src/main/AndroidManifest.xml
```

### 1.2 启动Android虚拟机
```bash
# 列出可用的AVD
emulator -list-avds

# 启动虚拟机（替换为您的AVD名称）
emulator -avd Pixel_4_API_30 -audio-in on -audio-out on
```

### 1.3 验证虚拟机连接
```bash
# 检查设备连接
adb devices

# 应该看到类似输出：
# emulator-5554    device
```

## 步骤2: 构建和安装应用

### 2.1 清理和构建
```bash
# 清理之前的构建
./gradlew clean

# 构建debug版本
./gradlew :briar-android:assembleDebug
```

### 2.2 安装到虚拟机
```bash
# 安装APK
adb install -r briar-android/build/outputs/apk/debug/briar-android-debug.apk

# 验证安装
adb shell pm list packages | grep briar
```

## 步骤3: 权限配置

### 3.1 授予录音权限
```bash
# 授予录音权限
adb shell pm grant org.briarproject.briar.android.debug android.permission.RECORD_AUDIO

# 授予其他必要权限
adb shell pm grant org.briarproject.briar.android.debug android.permission.CAMERA
adb shell pm grant org.briarproject.briar.android.debug android.permission.POST_NOTIFICATIONS
```

### 3.2 验证权限状态
```bash
# 检查权限状态
adb shell dumpsys package org.briarproject.briar.android.debug | grep -A 10 "declared permissions"
```

## 步骤4: 启动和初始化应用

### 4.1 启动应用
```bash
# 启动Briar应用
adb shell am start -n org.briarproject.briar.android.debug/org.briarproject.briar.android.splash.SplashScreenActivity
```

### 4.2 应用初始化
在虚拟机屏幕上：
1. 等待应用启动完成
2. 如果是首次运行：
   - 创建新账户
   - 设置用户名：`测试用户`
   - 设置密码：`test123456`
   - 完成初始化设置

## 步骤5: 语音消息功能测试

### 5.1 创建测试对话
1. 在主界面点击"+"按钮
2. 选择"添加联系人"
3. 或者使用现有的测试对话

### 5.2 测试录制功能
1. 进入聊天界面
2. 查找消息输入区域的麦克风图标
3. 长按麦克风按钮开始录制
4. 说话测试："这是一条语音消息测试"
5. 松开按钮停止录制
6. 验证录制状态和时长显示

### 5.3 测试发送功能
1. 录制完成后点击发送按钮
2. 验证语音消息出现在聊天界面
3. 检查UI组件：播放按钮、进度条、时长显示

### 5.4 测试播放功能
1. 点击语音消息的播放按钮
2. 验证音频播放
3. 检查进度条更新
4. 测试暂停/继续功能

## 步骤6: 实时调试监控

### 6.1 应用日志监控
```bash
# 实时查看Briar应用日志
adb logcat | grep -i briar
```

### 6.2 音频系统日志
```bash
# 监控音频相关日志
adb logcat | grep -i "audio\|media\|voice\|record"
```

### 6.3 权限相关日志
```bash
# 监控权限请求日志
adb logcat | grep -i "permission\|grant"
```

## 步骤7: 性能监控

### 7.1 资源使用监控
```bash
# 监控CPU和内存使用
adb shell top | grep briar

# 查看详细内存信息
adb shell dumpsys meminfo org.briarproject.briar.android.debug
```

### 7.2 文件系统检查
```bash
# 检查应用数据目录
adb shell ls -la /data/data/org.briarproject.briar.android.debug/

# 检查缓存目录（语音文件存储位置）
adb shell ls -la /data/data/org.briarproject.briar.android.debug/cache/voice_messages/
```

## 故障排除

### 问题1: 无法录制音频
**症状**: 点击麦克风按钮无反应或提示权限错误

**解决方案**:
```bash
# 重新授予录音权限
adb shell pm grant org.briarproject.briar.android.debug android.permission.RECORD_AUDIO

# 检查虚拟机音频设置
adb shell getprop | grep audio

# 重启应用
adb shell am force-stop org.briarproject.briar.android.debug
adb shell am start -n org.briarproject.briar.android.debug/org.briarproject.briar.android.splash.SplashScreenActivity
```

### 问题2: 应用崩溃
**症状**: 应用突然关闭或无响应

**解决方案**:
```bash
# 查看崩溃日志
adb logcat | grep -E "(FATAL|AndroidRuntime|CRASH)"

# 查看具体错误信息
adb logcat -b crash
```

### 问题3: 音频播放问题
**症状**: 录制成功但无法播放

**解决方案**:
```bash
# 检查音频文件
adb shell ls -la /data/data/org.briarproject.briar.android.debug/cache/voice_messages/

# 检查MediaPlayer日志
adb logcat | grep -i "mediaplayer\|audiotrack"
```

### 问题4: UI显示异常
**症状**: 语音消息控件显示不正常

**解决方案**:
```bash
# 检查布局相关日志
adb logcat | grep -i "layout\|view\|inflate"

# 重新安装应用
adb uninstall org.briarproject.briar.android.debug
adb install briar-android/build/outputs/apk/debug/briar-android-debug.apk
```

## 测试检查清单

### ✅ 基础功能
- [ ] 应用成功安装和启动
- [ ] 录音权限已授予
- [ ] 能够进入聊天界面
- [ ] 麦克风按钮可见且可点击

### ✅ 录制功能
- [ ] 长按开始录制
- [ ] 录制状态正确显示
- [ ] 录制时间计数正常
- [ ] 能够正常停止录制
- [ ] 支持取消录制

### ✅ 发送功能
- [ ] 录制完成后能发送
- [ ] 语音消息正确显示在聊天中
- [ ] UI组件完整显示

### ✅ 播放功能
- [ ] 播放按钮响应正常
- [ ] 音频能正常播放
- [ ] 进度条实时更新
- [ ] 支持暂停/继续
- [ ] 播放完成后状态重置

## 高级调试技巧

### 1. 使用Android Studio调试器
1. 在Android Studio中打开项目
2. 连接到运行中的应用进程
3. 在语音相关代码中设置断点
4. 逐步调试录制和播放流程

### 2. 网络抓包分析
```bash
# 如果需要分析语音消息的网络传输
adb shell tcpdump -i any -w /sdcard/capture.pcap
```

### 3. 自动化测试
```bash
# 运行语音消息相关的单元测试
./gradlew :briar-android:testDebugUnitTest --tests "*Voice*"
```

## 总结

通过以上步骤，您应该能够成功在Android虚拟机上调试Briar的语音消息功能。关键点包括：

1. ✅ 已修复AndroidManifest.xml中缺少的RECORD_AUDIO权限
2. ✅ 正确配置虚拟机音频支持
3. ✅ 授予必要的应用权限
4. ✅ 系统性地测试各项功能
5. ✅ 实时监控日志和性能

如果遇到问题，请参考故障排除部分或查看实时日志输出。
